# 🎮 趣味数学小天地

让孩子在游戏中快乐学习数学的互动教育游戏

## 🌐 在线体验

**🎯 立即开始游戏：** [https://w154594742.github.io/math-game-for-kids/](https://w154594742.github.io/math-game-for-kids/)

> 💡 无需下载安装，点击链接即可在浏览器中直接体验完整的数学学习游戏！

## 📖 项目简介

趣味数学小天地是一个专为儿童设计的数学学习游戏，通过生动有趣的场景和互动体验，帮助孩子掌握基础的四则运算。

## ✨ 主要功能

### 🎯 四种运算场景
- **加法场景** - 快乐购物：在商店里购买物品，学习加法运算
- **减法场景** - 分享时光：和朋友分享食物，学习减法运算  
- **乘法场景** - 花园种植：在花园里种植花朵，学习乘法运算
- **除法场景** - 公平分享：在派对上分享蛋糕，学习除法运算

### 🎨 游戏特色
- **学习伙伴**：可选择小熊、小猫、小兔作为学习伙伴
- **拖拽答题**：直观的拖拽操作，适合儿童使用
- **智能提示**：每个场景都有专门的提示功能，帮助理解运算过程
- **动画效果**：丰富的动画效果，增加学习趣味性
- **进度跟踪**：记录学习进度和得分

### 💡 提示功能
- **加法场景**：合并收集动画，展示"两部分合并成整体"
- **减法场景**：抛物线消失动画，展示"减少/消失"过程
- **乘法场景**：逐组计数动画，展示"重复相加/组数概念"
- **除法场景**：轮流分配动画，展示"平均分配"过程

## 🚀 技术特点

### 前端技术
- **纯原生JavaScript**：无框架依赖，轻量高效
- **模块化设计**：清晰的代码结构，易于维护
- **响应式布局**：适配不同屏幕尺寸
- **CSS3动画**：流畅的动画效果

### 部署技术
- **GitHub Pages**：免费的静态网站托管
- **GitHub Actions**：自动化CI/CD部署流程
- **零配置部署**：推送代码即自动更新线上版本
- **全球CDN**：GitHub提供的全球内容分发网络

### 架构设计
- **场景管理器**：统一管理四种运算场景
- **拖拽管理器**：处理拖拽交互逻辑
- **UI管理器**：统一的用户界面管理
- **数据管理器**：学习数据的存储和管理

## 📁 项目结构

```
├── index.html              # 主页面
├── css/                    # 样式文件
│   ├── main.css           # 主样式
│   ├── components.css     # 组件样式
│   ├── animations.css     # 动画样式
│   ├── scenes.css         # 场景样式
│   └── scenes/            # 场景专用样式
├── js/                     # JavaScript文件
│   ├── app.js             # 应用主入口
│   ├── game-engine.js     # 游戏引擎
│   ├── ui-manager.js      # UI管理器
│   ├── scene-manager.js   # 场景管理器
│   ├── drag-drop-manager.js # 拖拽管理器
│   ├── config/            # 配置文件
│   └── scenes/            # 场景类文件
└── README.md              # 项目说明
```

## 🎯 使用方法

1. 选择学习伙伴（小熊、小猫或小兔）
2. 选择运算类型（加法、减法、乘法或除法）
3. 设置题目难度
4. 开始游戏，通过拖拽方式答题
5. 使用提示功能了解运算过程
6. 查看得分和学习进度

## 🌟 教育价值

- **直观理解**：通过具体场景帮助孩子理解抽象的数学概念
- **动手操作**：拖拽交互增强学习体验
- **循序渐进**：从简单到复杂的题目设计
- **即时反馈**：实时的答题反馈和鼓励
- **寓教于乐**：在游戏中自然学习数学知识

## 📱 兼容性

- 支持现代浏览器（Chrome、Firefox、Safari、Edge）
- 响应式设计，支持桌面和移动设备
- 无需安装，直接在浏览器中运行

## 🌍 在线访问优势

- **🚀 即开即用**：无需下载安装，点击链接即可开始学习
- **📱 跨平台**：支持电脑、平板、手机等各种设备
- **🔄 自动更新**：游戏内容和功能持续优化，自动获取最新版本
- **🌐 随时随地**：只要有网络就能访问，学习不受地点限制
- **💾 免存储**：不占用设备存储空间，绿色环保

## 🔧 开发说明

本项目使用纯原生技术开发，无需构建工具，可直接在浏览器中运行。

### 本地运行
1. 克隆项目到本地
   ```bash
   git clone https://github.com/w154594742/math-game-for-kids.git
   cd math-game-for-kids
   ```
2. 使用本地服务器打开index.html（推荐使用Live Server）
3. 或直接在浏览器中打开index.html文件

### 🚀 自动部署

本项目已配置GitHub Actions自动部署到GitHub Pages：

#### 部署流程
- **触发条件**：推送代码到 `main` 分支时自动触发
- **部署地址**：[https://w154594742.github.io/math-game-for-kids/](https://w154594742.github.io/math-game-for-kids/)
- **部署状态**：[![Deploy to GitHub Pages](https://github.com/w154594742/math-game-for-kids/actions/workflows/deploy.yml/badge.svg)](https://github.com/w154594742/math-game-for-kids/actions/workflows/deploy.yml)

#### 手动部署
如需手动触发部署：
1. 进入GitHub仓库的 `Actions` 页面
2. 选择 `Deploy to GitHub Pages` 工作流
3. 点击 `Run workflow` 按钮

#### 部署配置
- **工作流文件**：`.github/workflows/deploy.yml`
- **部署分支**：`main`
- **部署方式**：GitHub Actions
- **访问地址**：自动生成的GitHub Pages URL

## 📄 许可证

本项目仅供学习和教育使用。

---

## 🎯 快速开始

**立即体验游戏：** [https://w154594742.github.io/math-game-for-kids/](https://w154594742.github.io/math-game-for-kids/)

🎮 让我们一起在游戏中快乐学习数学吧！

---

### 📞 联系方式

如有问题或建议，欢迎通过以下方式联系：
- 📧 提交 [GitHub Issue](https://github.com/w154594742/math-game-for-kids/issues)
- 🌟 给项目点个Star支持我们
- 🔄 Fork项目参与开发
