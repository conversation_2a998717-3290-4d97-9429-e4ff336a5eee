name: Deploy to GitHub Pages

on:
  push:
    branches: [ main ]
    # 可以指定只有特定文件变化时才触发部署
    paths-ignore:
      - 'README.md'
      - 'docs/**'
      - '.gitignore'
  
  # 允许手动触发部署
  workflow_dispatch:

# Sets permissions of the GITHUB_TOKEN to allow deployment to GitHub Pages
permissions:
  contents: read
  pages: write
  id-token: write

# Allow only one concurrent deployment, skipping runs queued between the run in-progress and latest queued.
# However, do NOT cancel in-progress runs as we want to allow these production deployments to complete.
concurrency:
  group: "pages"
  cancel-in-progress: false

jobs:
  # Build job
  build:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0  # 获取完整的git历史
      
      - name: Setup Pages
        uses: actions/configure-pages@v4
      
      # 可选：如果需要构建步骤，在这里添加
      # - name: Setup Node.js (if needed)
      #   uses: actions/setup-node@v4
      #   with:
      #     node-version: '18'
      #     cache: 'npm'
      # 
      # - name: Install dependencies (if needed)
      #   run: npm ci
      # 
      # - name: Build project (if needed)
      #   run: npm run build
      
      # 清理不需要的文件（可选）
      - name: Clean up unnecessary files
        run: |
          # 删除开发相关文件
          rm -rf .git
          rm -rf .github
          rm -f .gitignore
          # 删除测试文件（如果不需要在生产环境中）
          rm -f test-*.html
          # 保留主要的游戏文件
          echo "Cleaned up development files"
      
      - name: Upload artifact
        uses: actions/upload-pages-artifact@v3
        with:
          # Upload entire repository (after cleanup)
          path: '.'

  # Deployment job
  deploy:
    environment:
      name: github-pages
      url: ${{ steps.deployment.outputs.page_url }}
    runs-on: ubuntu-latest
    needs: build
    steps:
      - name: Deploy to GitHub Pages
        id: deployment
        uses: actions/deploy-pages@v4
        
      # 可选：部署成功后的通知
      - name: Deployment notification
        if: success()
        run: |
          echo "🎉 Successfully deployed to GitHub Pages!"
          echo "📱 Site URL: ${{ steps.deployment.outputs.page_url }}"
