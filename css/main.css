/* 趣味数学小天地 - 主样式文件 */

:root {
  --primary-color: #4A90E2;
  --secondary-color: #F5A623;
  --success-color: #7ED321;
  --error-color: #D0021B;
  --text-color: #333;
  --bg-color: #F8F9FA;
  --drag-shadow: 0 4px 8px rgba(0,0,0,0.2);
  --drop-zone-border: 2px dashed #4A90E2;
}

/* 基础样式重置 */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  font-family: 'Arial', sans-serif;
  background-color: var(--bg-color);
  color: var(--text-color);
  line-height: 1.6;
}

/* 布局样式 */
.view {
  display: none;
  width: 100%;
  min-height: 100vh;
  padding: 20px;
}

.view.active {
  display: block;
}

/* 通用组件样式 */
.btn {
  padding: 12px 24px;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 16px;
  font-weight: bold;
  transition: all 0.3s ease;
  background-color: var(--primary-color);
  color: white;
}

.btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0,0,0,0.2);
}

.btn:active {
  transform: translateY(0);
}

.btn-secondary {
  background-color: var(--secondary-color);
}

.btn-success {
  background-color: var(--success-color);
}

.btn-hint {
  background-color: #FF9800;
  color: white;
}

.btn-hint:hover {
  background-color: #F57C00;
}

.btn.disabled,
.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  pointer-events: none;
}

.btn-large {
  padding: 16px 32px;
  font-size: 18px;
  min-width: 120px;
  min-height: 60px;
}

/* 页面头部样式 */
.game-header {
  text-align: center;
  padding: 20px;
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  color: white;
  border-radius: 12px;
  margin-bottom: 20px;
  box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.game-header h1 {
  font-size: 2.5em;
  margin: 0 0 10px 0;
  text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.subtitle {
  font-size: 1.2em;
  margin: 0;
  opacity: 0.9;
}

/* 主要内容区域 */
.game-main {
  max-width: 800px;
  margin: 0 auto;
  padding: 0 20px;
}

/* 选择区域通用样式 */
.character-selection,
.operation-selection,
.difficulty-selection,
.start-section {
  margin-bottom: 25px;
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 15px;
}

.character-selection h2,
.operation-selection h2,
.difficulty-selection h2 {
  font-size: 1.8em;
  color: var(--text-color);
  margin-bottom: 20px;
}

/* 角色选择样式 */
.character-grid {
  display: flex;
  justify-content: center;
  gap: 20px;
  flex-wrap: wrap;
}

.character-card {
  background: white;
  border: 3px solid transparent;
  border-radius: 12px;
  padding: 10px;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  min-width: 100px;
}

.character-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 6px 16px rgba(0,0,0,0.2);
}

.character-card.selected {
  border-color: var(--primary-color);
  background: rgba(74,144,226,0.1);
}

.character-avatar {
  font-size: 2.2em;
  margin-bottom: 5px;
}

.character-card span {
  display: block;
  font-weight: bold;
  color: var(--text-color);
}

/* 运算类型选择样式 */
.operation-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  max-width: 600px;
  margin: 0 auto;
}

.operation-btn {
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  font-weight: bold;
}

.operation-btn.selected {
  background-color: var(--success-color);
  transform: scale(1.05);
}

/* 难度选择样式 */
.difficulty-grid {
  display: flex;
  justify-content: center;
  gap: 20px;
  flex-wrap: wrap;
}

.difficulty-btn {
  min-width: 150px;
  height: 60px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  line-height: 1.2;
}

.difficulty-btn small {
  font-size: 12px;
  opacity: 0.8;
  margin-top: 5px;
}

.difficulty-btn.selected {
  background-color: var(--success-color);
  transform: scale(1.05);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .game-header h1 {
    font-size: 2em;
  }

  .subtitle {
    font-size: 1em;
  }

  .game-main {
    padding: 0 15px;
  }

  .character-grid {
    gap: 15px;
  }

  .character-card {
    min-width: 80px;
    padding: 15px;
  }

  .character-avatar {
    font-size: 2.5em;
  }

  .operation-grid {
    grid-template-columns: 1fr;
    gap: 15px;
  }

  .operation-btn {
    height: 60px;
    font-size: 14px;
  }

  .difficulty-grid {
    flex-direction: column;
    align-items: center;
    gap: 15px;
  }

  .difficulty-btn {
    min-width: 200px;
    height: 60px;
  }

  .game-info {
    flex-direction: column;
    gap: 10px;
    text-align: center;
  }

  .stats-grid {
    grid-template-columns: 1fr;
    gap: 15px;
  }

  .big-number {
    font-size: 3em;
  }

  .continue-section .btn {
    display: block;
    margin: 10px auto;
    width: 200px;
  }
}

@media (max-width: 480px) {
  .view {
    padding: 15px;
  }

  .game-header {
    padding: 15px;
    margin-bottom: 15px;
  }

  .game-header h1 {
    font-size: 1.8em;
  }

  .character-selection,
  .operation-selection,
  .difficulty-selection,
  .start-section {
    margin-bottom: 20px;
  }

  .character-selection h2,
  .operation-selection h2,
  .difficulty-selection h2 {
    font-size: 1.5em;
  }

  .btn-large {
    padding: 12px 24px;
    font-size: 16px;
    min-height: 50px;
  }

  .scene-container {
    min-height: 500px; /* 改为最小高度，允许动态调整 */
  }

  .question-text {
    font-size: 20px;
  }

  .drag-element {
    width: 50px;
    height: 50px;
  }

  .drop-zone {
    min-width: 60px;
    min-height: 60px;
  }
}
