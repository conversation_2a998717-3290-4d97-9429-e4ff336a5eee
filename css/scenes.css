/* 场景样式文件 */

/* 场景动画 */
@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes bounce {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-15px);
  }
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes drift {
  0%, 100% {
    transform: translateX(0px);
  }
  50% {
    transform: translateX(30px);
  }
}

@keyframes flutter {
  0%, 100% {
    transform: translate(0px, 0px) rotate(0deg);
  }
  25% {
    transform: translate(10px, -5px) rotate(5deg);
  }
  50% {
    transform: translate(-5px, -10px) rotate(-3deg);
  }
  75% {
    transform: translate(-10px, -5px) rotate(3deg);
  }
}

@keyframes sway {
  0%, 100% {
    transform: rotate(-2deg);
  }
  50% {
    transform: rotate(2deg);
  }
}

@keyframes wiggle {
  0%, 100% {
    transform: rotate(0deg);
  }
  25% {
    transform: rotate(5deg);
  }
  75% {
    transform: rotate(-5deg);
  }
}

/* 场景标题样式 */
.scene-title {
  font-family: 'Comic Sans MS', cursive, sans-serif;
  text-shadow: 1px 1px 2px rgba(0,0,0,0.1);
  user-select: none;
  pointer-events: none;
}

/* 购物场景样式 */
.shopping-shelf {
  position: relative;
}

.shopping-shelf::before {
  content: '';
  position: absolute;
  top: -10px;
  left: 10px;
  right: 10px;
  height: 5px;
  background: #654321;
  border-radius: 2px;
}

.shopping-cart {
  transition: all 0.3s ease;
  user-select: none;
}

.shopping-cart:hover {
  transform: scale(1.05);
}

/* 分享场景样式 */
.picnic-table {
  position: relative;
}

.picnic-table::before {
  content: '';
  position: absolute;
  bottom: -20px;
  left: 20px;
  right: 20px;
  height: 8px;
  background: #8B4513;
  border-radius: 4px;
}

.friend-plate {
  transition: all 0.3s ease;
  user-select: none;
}

.friend-plate:hover {
  transform: scale(1.1);
}

/* 花园场景样式 */
.flower-garden {
  backdrop-filter: blur(10px);
  border: 2px solid rgba(76,175,80,0.3);
}

.flower-plot {
  transition: all 0.3s ease;
  position: relative;
}

.flower-plot:hover {
  transform: scale(1.05);
  border-color: #4CAF50;
}

.flower-plot::before {
  content: '';
  position: absolute;
  bottom: 2px;
  left: 50%;
  transform: translateX(-50%);
  width: 20px;
  height: 3px;
  background: #8B4513;
  border-radius: 2px;
}

/* 派对场景样式 */
.party-table {
  position: relative;
}

.party-table::before {
  content: '🎂';
  position: absolute;
  top: -20px;
  left: 50%;
  transform: translateX(-50%);
  font-size: 24px;
}

/* 除法场景盘子样式已移至 css/scenes/party-scene.css */

/* 拖拽元素增强样式 */
.drag-element {
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}

.drag-element:hover {
  transform: scale(1.1);
  box-shadow: 0 4px 8px rgba(0,0,0,0.3);
}

.drag-element.dragging {
  opacity: 0.8;
  transform: scale(1.2) rotate(5deg);
  z-index: 1000;
}

/* 拖拽目标区域增强样式 */
.drop-zone {
  user-select: none;
}

.drop-zone.drag-over {
  transform: scale(1.05);
  border-style: solid;
  box-shadow: 0 0 15px rgba(74,144,226,0.5);
}

.drop-zone.filled {
  animation: successPulse 0.6s ease-in-out;
}

.drop-zone.error-feedback {
  animation: errorShake 0.5s ease-in-out;
  border-color: var(--error-color);
}

.drop-zone-label {
  font-family: 'Comic Sans MS', cursive, sans-serif;
  text-shadow: 1px 1px 2px rgba(0,0,0,0.1);
}

.drop-zone-counter {
  font-family: 'Arial', sans-serif;
  text-shadow: 1px 1px 2px rgba(0,0,0,0.1);
}

/* 场景容器增强 */
.scene-container {
  position: relative;
  overflow: visible; /* 确保内容不被裁剪 */
  border: 2px solid rgba(255,255,255,0.3);
  backdrop-filter: blur(5px);
  height: auto !important; /* 强制使用自动高度，覆盖其他固定高度设置 */
}

.scene-container.celebration {
  animation: celebrate 1s ease-in-out;
}

.scene-container.error-feedback {
  animation: errorShake 0.5s ease-in-out;
}

/* 响应式场景设计 */
@media (max-width: 768px) {
  .scene-container {
    min-height: 300px; /* 改为最小高度，允许动态调整 */
  }
  
  .scene-title {
    font-size: 14px;
    padding: 6px 12px;
  }
  
  .shopping-cart,
  .friend-plate {
    width: 80px;
    height: 60px;
    font-size: 12px;
  }

  /* party-plate 响应式样式已移至 css/scenes/party-scene.css */
  
  .flower-plot {
    width: 45px;
    height: 45px;
    font-size: 8px;
  }
  
  .drag-element {
    width: 40px;
    height: 40px;
    font-size: 20px;
  }
  
  .drop-zone {
    min-width: 60px;
    min-height: 60px;
  }
  
  .drop-zone-label {
    font-size: 10px;
  }
  
  .drop-zone-counter {
    font-size: 14px;
  }
}

@media (max-width: 480px) {
  .scene-container {
    min-height: 250px; /* 改为最小高度，允许动态调整 */
  }
  
  .scene-title {
    font-size: 12px;
    padding: 4px 8px;
  }
  
  .shopping-cart,
  .friend-plate {
    width: 60px;
    height: 50px;
    font-size: 10px;
  }

  /* party-plate 响应式样式已移至 css/scenes/party-scene.css */
  
  .flower-plot {
    width: 35px;
    height: 35px;
    font-size: 6px;
  }
  
  .drag-element {
    width: 35px;
    height: 35px;
    font-size: 18px;
  }
  
  .drop-zone {
    min-width: 50px;
    min-height: 50px;
  }
  
  .drop-zone-label {
    font-size: 8px;
  }
  
  .drop-zone-counter {
    font-size: 12px;
  }
}

/* 场景切换动画 */
.scene-transition-enter {
  opacity: 0;
  transform: scale(0.8);
}

.scene-transition-enter-active {
  opacity: 1;
  transform: scale(1);
  transition: all 0.5s ease-out;
}

.scene-transition-exit {
  opacity: 1;
  transform: scale(1);
}

.scene-transition-exit-active {
  opacity: 0;
  transform: scale(1.2);
  transition: all 0.3s ease-in;
}

/* ===== 加法场景优化样式 ===== */

/* 渐变背景动画 - 已移除，改为静态绿色背景 */

/* 数字浮动动画 */
@keyframes numberFloat {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-8px); }
}

/* 篮子呼吸动画 */
@keyframes basketBreathe {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.05); }
}

/* 物品闪烁动画 */
@keyframes itemGlow {
  0%, 100% {
    filter: brightness(1) drop-shadow(0 0 5px rgba(255, 215, 0, 0.3));
  }
  50% {
    filter: brightness(1.2) drop-shadow(0 0 15px rgba(255, 215, 0, 0.8));
  }
}

/* 波纹扩散动画 */
@keyframes ripple {
  0% {
    transform: scale(0);
    opacity: 1;
  }
  100% {
    transform: scale(4);
    opacity: 0;
  }
}

/* 烟花效果动画 */
@keyframes firework {
  0% { transform: scale(0) rotate(0deg); opacity: 1; }
  50% { transform: scale(1.5) rotate(180deg); opacity: 0.8; }
  100% { transform: scale(3) rotate(360deg); opacity: 0; }
}

/* 场景背景优化 - 统一绿色系 */
.scene-container.shopping-scene {
  background: linear-gradient(135deg, #E8F5E8, #C8E6C8);
}

.scene-container.sharing-scene {
  background: linear-gradient(135deg, #F0FFF0, #D4F4D4);
}

.scene-container.garden-scene {
  background: linear-gradient(135deg, #E5FFE5, #B8E6B8);
}

.scene-container.party-scene {
  background: linear-gradient(135deg, #F5FFF5, #E0F2E0);
}

/* 标题区域优化 - 所有场景通用 */
.shopping-scene .title-header,
.sharing-scene .title-header,
.garden-scene .title-header,
.party-scene .title-header {
  animation: titleFloat 4s ease-in-out infinite;
  position: relative;
  overflow: visible;
}

.shopping-scene .title-header::before,
.sharing-scene .title-header::before,
.garden-scene .title-header::before,
.party-scene .title-header::before {
  content: '🌟';
  position: absolute;
  left: -40px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 32px;
  animation: sparkle 2s ease-in-out infinite;
}

.shopping-scene .title-header::after,
.sharing-scene .title-header::after,
.garden-scene .title-header::after,
.party-scene .title-header::after {
  content: '🌟';
  position: absolute;
  right: -40px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 32px;
  animation: sparkle 2s ease-in-out infinite 1s;
}

/* 标题浮动动画 */
@keyframes titleFloat {
  0%, 100% { transform: translateX(-50%) translateY(0px); }
  50% { transform: translateX(-50%) translateY(-5px); }
}

/* 星星闪烁动画 */
@keyframes sparkle {
  0%, 100% {
    transform: translateY(-50%) scale(1);
    filter: brightness(1);
  }
  50% {
    transform: translateY(-50%) scale(1.2);
    filter: brightness(1.5);
  }
}

/* 数字拖拽区优化 */
.shopping-scene .number-drag-area {
  background: linear-gradient(45deg, rgba(255,255,255,0.95), rgba(255,248,220,0.95));
  border-radius: 20px;
  backdrop-filter: blur(10px);
}

/* 数字元素优化 */
.shopping-scene .number-drag-area .number-box {
  animation: numberFloat 3s ease-in-out infinite;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 数字悬停效果 */
.shopping-scene .number-drag-area .number-box:hover {
  transform: translateY(-3px) scale(1.05);
  filter: brightness(1.1);
}

/* 篮子容器优化 */
.shopping-scene .adaptive-basket {
  animation: basketBreathe 4s ease-in-out infinite;
  position: relative;
  overflow: visible;
}

/* 篮子标签优化 */
.shopping-scene .basket-label {
  position: relative;
}

/* 篮子标签装饰 */
.shopping-scene .basket-label::before {
  content: '✨';
  position: absolute;
  left: -10px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 16px;
  animation: float 2s ease-in-out infinite;
}

.shopping-scene .basket-label::after {
  content: '✨';
  position: absolute;
  right: -10px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 16px;
  animation: float 2s ease-in-out infinite 1s;
}

/* 篮子内物品优化 */
.shopping-scene .basket-items .item {
  animation: itemGlow 3s ease-in-out infinite;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.shopping-scene .basket-items .item:hover {
  transform: scale(1.1) rotate(5deg);
  filter: brightness(1.2);
}

/* 题目文字优化 */
.shopping-scene .question-text {
  font-family: 'Comic Sans MS', cursive, sans-serif;
  position: relative;
  overflow: visible;
}

/* 题目装饰云朵 */
.shopping-scene .question-text::before {
  content: '☁️';
  position: absolute;
  left: -30px;
  top: -15px;
  font-size: 24px;
  animation: drift 6s ease-in-out infinite;
}

.shopping-scene .question-text::after {
  content: '☁️';
  position: absolute;
  right: -30px;
  top: -15px;
  font-size: 20px;
  animation: drift 6s ease-in-out infinite 3s;
}

/* 除法场景问题文字样式已移至 css/scenes/party-scene.css */

/* 云朵漂移动画 */
@keyframes drift {
  0%, 100% { transform: translateX(0px) translateY(0px); }
  25% { transform: translateX(10px) translateY(-5px); }
  50% { transform: translateX(-5px) translateY(-10px); }
  75% { transform: translateX(5px) translateY(-3px); }
}

/* 浮动动画 */
@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

/* 答案拖拽区优化 */
.shopping-scene .answer-drop-area {
  position: relative;
  overflow: visible;
}

/* 答案区域装饰 */
.shopping-scene .answer-drop-area::before {
  content: '🎯';
  position: absolute;
  left: 50%;
  top: -25px;
  transform: translateX(-50%);
  font-size: 30px;
  animation: bounce 2s ease-in-out infinite;
}

/* 弹跳动画 */
@keyframes bounce {
  0%, 100% { transform: translateX(-50%) translateY(0px); }
  50% { transform: translateX(-50%) translateY(-10px); }
}

/* 点击波纹效果 */
.ripple-effect {
  position: absolute;
  border-radius: 50%;
  background: radial-gradient(circle, rgba(255, 105, 180, 0.6), transparent);
  animation: ripple 0.6s linear;
  pointer-events: none;
}

/* 成功烟花效果 */
.firework-effect {
  position: absolute;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: radial-gradient(circle, #FFD700, #FF69B4, #00CED1);
  animation: firework 1s ease-out;
  pointer-events: none;
}

/* 篮子内物品优化 */
.basket-items .item {
  animation: itemGlow 3s ease-in-out infinite !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  filter: drop-shadow(0 2px 8px rgba(0,0,0,0.3)) !important;
}

.basket-items .item:hover {
  transform: scale(1.2) rotate(10deg) !important;
  filter: brightness(1.3) drop-shadow(0 4px 15px rgba(255, 215, 0, 0.8)) !important;
}

/* 题目文字优化 */
.question-text {
  background: linear-gradient(135deg, rgba(255,255,255,0.95), rgba(255,248,220,0.95)) !important;
  border: 3px solid transparent !important;
  background-clip: padding-box !important;
  border-image: linear-gradient(45deg, #FF6B6B, #4ECDC4, #45B7D1) 1 !important;
  border-radius: 25px !important;
  box-shadow: 0 10px 30px rgba(0,0,0,0.2), inset 0 2px 0 rgba(255,255,255,0.8) !important;
  font-family: 'Comic Sans MS', cursive, sans-serif !important;
  font-weight: bold !important;
  text-shadow: 2px 2px 4px rgba(0,0,0,0.1) !important;
  /* position: relative !important; */ /* 移除通用position设置，让各场景自行控制 */
  overflow: visible !important;
}

/* 题目装饰云朵 */
.question-text::before {
  content: '☁️';
  position: absolute;
  left: -30px;
  top: -15px;
  font-size: 24px;
  animation: drift 6s ease-in-out infinite;
}

.question-text::after {
  content: '☁️';
  position: absolute;
  right: -30px;
  top: -15px;
  font-size: 20px;
  animation: drift 6s ease-in-out infinite 3s;
}
